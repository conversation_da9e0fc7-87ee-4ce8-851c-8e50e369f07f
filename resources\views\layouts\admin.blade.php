<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>@yield('title', 'Admin Dashboard') - {{ config('app.name', 'Atrix Logistics') }}</title>

    <!-- Favicon -->
    @if(!empty($siteSettings['site_favicon']) && str_starts_with($siteSettings['site_favicon'], 'uploads/'))
        <link rel="icon" href="{{ Storage::url($siteSettings['site_favicon']) }}" type="image/x-icon">
        <link rel="shortcut icon" href="{{ Storage::url($siteSettings['site_favicon']) }}" type="image/x-icon">
    @else
        <link rel="icon" href="{{ asset('assets/images/favicon.ico') }}" type="image/x-icon">
        <link rel="shortcut icon" href="{{ asset('assets/images/favicon.ico') }}" type="image/x-icon">
    @endif

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <style>
        body {
            overflow-x: hidden;
        }

        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            position: fixed;
            top: 0;
            left: 0;
            width: 280px;
            z-index: 1000;
            transform: translateX(-100%);
            transition: transform 0.3s ease;
            overflow-y: auto;
            overflow-x: hidden;
        }

        .sidebar.show {
            transform: translateX(0);
        }

        .sidebar-content {
            height: calc(100vh - 160px);
            overflow-y: auto;
            padding-bottom: 20px;
            /* Hide scrollbar for Chrome, Safari and Opera */
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* Internet Explorer 10+ */
        }

        .sidebar-content::-webkit-scrollbar {
            display: none; /* Hide scrollbar for Chrome, Safari and Opera */
        }

        .sidebar-footer {
            position: fixed;
            bottom: 0;
            left: 0;
            width: 280px;
            background: rgba(0, 0, 0, 0.2);
            padding: 20px 15px;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
            z-index: 1001;
            transform: translateX(-100%);
            transition: transform 0.3s ease;
        }

        .sidebar.show .sidebar-footer {
            transform: translateX(0);
        }

        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            border-radius: 8px;
            margin: 2px 0;
            transition: all 0.3s ease;
            padding: 12px 15px;
        }
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
        }

        .main-content {
            background-color: #f8f9fa;
            min-height: 100vh;
            margin-left: 0;
            transition: all 0.3s ease;
            width: 100vw;
            max-width: 100%;
            overflow-x: hidden;
        }

        .main-content.sidebar-open {
            margin-left: 280px;
            width: calc(100vw - 280px);
        }

        .main-content .container-fluid {
            padding-left: 15px;
            padding-right: 15px;
            max-width: 100%;
            overflow-x: hidden;
        }

        .sidebar-toggle {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1001;
            background: #667eea;
            border: none;
            color: white;
            width: 50px;
            height: 50px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
        }

        .sidebar-toggle:hover {
            background: #5a67d8;
            transform: scale(1.05);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        .sidebar-toggle.sidebar-open {
            left: 300px;
            background: #5a67d8;
        }

        .sidebar-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: rgba(0, 0, 0, 0.5);
            z-index: 999;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .sidebar-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        /* Responsive adjustments */
        @media (max-width: 991.98px) {
            .main-content.sidebar-open {
                margin-left: 0;
                width: 100vw;
            }

            .sidebar-toggle.sidebar-open {
                left: 20px;
            }
        }

        @media (max-width: 768px) {
            .sidebar-toggle {
                top: 15px;
                left: 15px;
                width: 45px;
                height: 45px;
                font-size: 16px;
            }

            .sidebar-toggle.sidebar-open {
                left: 15px;
            }

            .main-content .container-fluid {
                padding-left: 10px;
                padding-right: 10px;
            }
        }

        /* Prevent horizontal scroll on all elements */
        * {
            box-sizing: border-box;
        }

        .table-responsive {
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            transition: all 0.3s ease;
        }
        .card:hover {
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .stat-card-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }
        .stat-card-warning {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
        }
        .stat-card-danger {
            background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);
        }
        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
        }
        .table th {
            border-top: none;
            font-weight: 600;
            color: #495057;
        }
        .badge {
            font-size: 0.75rem;
            padding: 0.5em 0.75em;
        }
        .btn {
            border-radius: 8px;
            font-weight: 500;
        }
        .form-control, .form-select {
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }
        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        /* Admin Pagination Fixes - Override any conflicting styles */
        .pagination {
            margin-bottom: 0;
        }

        .pagination .page-item {
            margin: 0 2px !important;
        }

        .pagination .page-link {
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            padding: 0.5rem 0.75rem !important;
            min-width: 40px !important;
            height: 40px !important;
            font-size: 14px !important;
            line-height: 1.25 !important;
            color: #6c757d !important;
            text-decoration: none !important;
            background-color: #fff !important;
            border: 1px solid #dee2e6 !important;
            border-radius: 0.375rem !important;
            transition: all 0.15s ease-in-out !important;
        }

        .pagination .page-link:hover {
            color: #0056b3 !important;
            background-color: #e9ecef !important;
            border-color: #dee2e6 !important;
        }

        .pagination .page-item.active .page-link {
            color: #fff !important;
            background-color: #667eea !important;
            border-color: #667eea !important;
        }

        .pagination .page-item.disabled .page-link {
            color: #6c757d !important;
            background-color: #fff !important;
            border-color: #dee2e6 !important;
            opacity: 0.5 !important;
        }

        /* Fix pagination icon sizes specifically */
        .pagination .page-link i.fas {
            font-size: 12px !important;
            line-height: 1 !important;
        }

        /* Override any global pagination styles that might conflict */
        .pagination li {
            height: auto !important;
            width: auto !important;
            line-height: normal !important;
            font-size: 14px !important;
            margin: 0 2px !important;
            position: relative !important;
            display: inline-block !important;
        }

        .pagination li a,
        .pagination li span {
            height: 40px !important;
            width: auto !important;
            min-width: 40px !important;
            line-height: normal !important;
            font-size: 14px !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            position: relative !important;
            z-index: 1 !important;
        }

        /* Responsive pagination */
        @media (max-width: 576px) {
            .pagination .page-link {
                padding: 0.375rem 0.5rem !important;
                font-size: 12px !important;
                min-width: 35px !important;
                height: 35px !important;
            }

            .pagination .page-link i.fas {
                font-size: 10px !important;
            }
        }

        /* Global Search Styles */
        #globalSearch {
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
            padding-right: 45px;
        }

        #globalSearch:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        #searchResults {
            border: 1px solid #dee2e6;
            border-top: none;
            max-height: 400px;
            overflow-y: auto;
        }

        .search-result-item {
            padding: 12px 16px;
            border-bottom: 1px solid #f8f9fa;
            cursor: pointer;
            transition: background-color 0.2s ease;
            text-decoration: none;
            color: inherit;
            display: block;
        }

        .search-result-item:hover {
            background-color: #f8f9fa;
            text-decoration: none;
            color: inherit;
        }

        .search-result-item:last-child {
            border-bottom: none;
        }

        .search-result-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            color: white;
            margin-right: 12px;
            flex-shrink: 0;
        }

        .search-result-content {
            flex: 1;
            min-width: 0;
        }

        .search-result-title {
            font-weight: 600;
            font-size: 14px;
            margin-bottom: 2px;
            color: #495057;
        }

        .search-result-subtitle {
            font-size: 12px;
            color: #6c757d;
            margin-bottom: 2px;
        }

        .search-result-description {
            font-size: 11px;
            color: #868e96;
        }

        .search-result-type {
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 4px;
            background-color: #e9ecef;
            color: #495057;
            text-transform: uppercase;
            font-weight: 500;
            letter-spacing: 0.5px;
        }

        .search-loading {
            padding: 20px;
            text-align: center;
            color: #6c757d;
        }

        .search-no-results {
            padding: 20px;
            text-align: center;
            color: #6c757d;
        }

        /* Color variations for different entity types */
        .search-result-icon.user { background-color: #007bff; }
        .search-result-icon.order { background-color: #28a745; }
        .search-result-icon.parcel { background-color: #6f42c1; }
        .search-result-icon.product { background-color: #fd7e14; }
        .search-result-icon.category { background-color: #20c997; }
        .search-result-icon.career { background-color: #6610f2; }
        .search-result-icon.job_application { background-color: #e83e8c; }
        .search-result-icon.blog_post { background-color: #17a2b8; }
        .search-result-icon.contact { background-color: #ffc107; }
        .search-result-icon.newsletter_subscriber { background-color: #6c757d; }
        .search-result-icon.carrier { background-color: #dc3545; }

        /* Mobile search improvements */
        @media (max-width: 767.98px) {
            .main-content {
                margin-top: 60px;
            }

            .d-flex.justify-content-between.flex-wrap.flex-md-nowrap.align-items-center {
                margin-top: 60px !important;
            }
        }

        /* Search result active state for keyboard navigation */
        .search-result-item.active {
            background-color: #e3f2fd !important;
            border-left: 4px solid #667eea;
        }
    </style>

    @stack('styles')
</head>
<body class="admin-layout">
    <!-- Sidebar Toggle Button -->
    <button class="sidebar-toggle" id="sidebarToggle">
        <i class="fas fa-chevron-right" id="toggleIcon"></i>
    </button>

    <!-- Sidebar Overlay -->
    <div class="sidebar-overlay" id="sidebarOverlay"></div>

    <!-- Sidebar -->
    <nav class="sidebar" id="sidebar">
        <div class="p-3">
            <div class="text-center mb-4">
                <h4 class="navbar-brand mb-0">
                    <i class="fas fa-shipping-fast me-2"></i>
                    Atrix Admin
                </h4>
                <small class="text-white-50">Welcome, {{ auth()->user()->name }}</small>
            </div>

            <div class="sidebar-content">
                <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('admin.dashboard') ? 'active' : '' }}" href="{{ route('admin.dashboard') }}">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                Dashboard
                            </a>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle {{ request()->routeIs('admin.parcels.*', 'admin.analytics.parcels*') ? 'active' : '' }}"
                               href="#" id="parcelsDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-box me-2"></i>
                                Parcels
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{{ route('admin.parcels.index') }}">
                                    <i class="fas fa-list me-2"></i> Manage Parcels
                                </a></li>
                                <li><a class="dropdown-item" href="{{ route('admin.analytics.parcels') }}">
                                    <i class="fas fa-chart-line me-2"></i> Parcel Analytics
                                </a></li>
                                <li><a class="dropdown-item" href="{{ route('admin.parcels.create') }}">
                                    <i class="fas fa-plus me-2"></i> Create Parcel
                                </a></li>
                            </ul>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('admin.reports.*') ? 'active' : '' }}" href="{{ route('admin.reports.index') }}">
                                <i class="fas fa-chart-bar me-2"></i>
                                Reports
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('admin.quotes.*') ? 'active' : '' }}" href="{{ route('admin.quotes.index') }}">
                                <i class="fas fa-quote-left me-2"></i>
                                Quotes
                            </a>
                        </li>

                        <!-- Blog Management -->
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle {{ request()->routeIs('admin.blog.*') ? 'active' : '' }}"
                               href="#" id="blogDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-blog me-2"></i>
                                Blog
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item {{ request()->routeIs('admin.blog.index') ? 'active' : '' }}"
                                       href="{{ route('admin.blog.index') }}">
                                    <i class="fas fa-list me-2"></i> All Posts
                                </a></li>
                                <li><a class="dropdown-item {{ request()->routeIs('admin.blog.create') ? 'active' : '' }}"
                                       href="{{ route('admin.blog.create') }}">
                                    <i class="fas fa-plus me-2"></i> Create Post
                                </a></li>
                            </ul>
                        </li>

                        <!-- Jobs Management -->
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle {{ request()->routeIs('admin.cms.careers.*', 'admin.cms.job-applications.*') ? 'active' : '' }}"
                               href="#" id="jobsDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-briefcase me-2"></i>
                                Jobs
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item {{ request()->routeIs('admin.cms.careers.*') ? 'active' : '' }}"
                                       href="{{ route('admin.cms.careers.index') }}">
                                    <i class="fas fa-list me-2"></i> Job Posts
                                </a></li>
                                <li><a class="dropdown-item {{ request()->routeIs('admin.cms.careers.create') ? 'active' : '' }}"
                                       href="{{ route('admin.cms.careers.create') }}">
                                    <i class="fas fa-plus me-2"></i> Create Job
                                </a></li>
                                <li><a class="dropdown-item {{ request()->routeIs('admin.cms.job-applications.*') ? 'active' : '' }}"
                                       href="{{ route('admin.cms.job-applications.index') }}">
                                    <i class="fas fa-file-alt me-2"></i> Applications
                                </a></li>
                            </ul>
                        </li>

                        <!-- CMS Management -->
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle {{ request()->routeIs('admin.cms.settings.*', 'admin.cms.team.*', 'admin.cms.sliders.*') ? 'active' : '' }}"
                               href="#" id="cmsDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-edit me-2"></i>
                                CMS Management
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item {{ request()->routeIs('admin.cms.settings.*') ? 'active' : '' }}"
                                       href="{{ route('admin.cms.settings.index') }}">
                                    <i class="fas fa-cog me-2"></i> Site Settings
                                </a></li>
                                <li><a class="dropdown-item {{ request()->routeIs('admin.cms.team.*') ? 'active' : '' }}"
                                       href="{{ route('admin.cms.team.index') }}">
                                    <i class="fas fa-users me-2"></i> Team Members
                                </a></li>
                                <li><a class="dropdown-item {{ request()->routeIs('admin.cms.sliders.*') ? 'active' : '' }}"
                                       href="{{ route('admin.cms.sliders.index') }}">
                                    <i class="fas fa-images me-2"></i> Sliders & Banners
                                </a></li>
                            </ul>
                        </li>

                        <!-- Communications -->
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle {{ request()->routeIs('admin.communications.*') ? 'active' : '' }}"
                               href="#" id="communicationsDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-envelope me-2"></i>
                                Communications
                                @php
                                    $newContactsCount = cache()->remember('admin_new_contacts_count', 300, function() {
                                        return \App\Models\Contact::where('status', 'new')->count();
                                    });
                                @endphp
                                @if($newContactsCount > 0)
                                    <span class="badge bg-danger ms-1">{{ $newContactsCount }}</span>
                                @endif
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item {{ request()->routeIs('admin.communications.live-chat.index') ? 'active' : '' }}"
                                       href="{{ route('admin.communications.live-chat.index') }}">
                                    <i class="fas fa-comments me-2"></i> Live Chat
                                    @php
                                        $liveChatUnreadCount = cache()->remember('admin_live_chat_unread_count', 60, function() {
                                            return \App\Models\LiveChatMessage::fromVisitor()->unread()->count();
                                        });
                                    @endphp
                                    @if($liveChatUnreadCount > 0)
                                        <span class="badge bg-danger ms-1">{{ $liveChatUnreadCount }}</span>
                                    @endif
                                </a></li>
                                <li><a class="dropdown-item {{ request()->routeIs('admin.communications.live-chat.history') ? 'active' : '' }}"
                                       href="{{ route('admin.communications.live-chat.history') }}">
                                    <i class="fas fa-history me-2"></i> Chat History
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item {{ request()->routeIs('admin.communications.contacts.*') ? 'active' : '' }}"
                                       href="{{ route('admin.communications.contacts.index') }}">
                                    <i class="fas fa-inbox me-2"></i> Contact Messages
                                    @if($newContactsCount > 0)
                                        <span class="badge bg-danger ms-1">{{ $newContactsCount }}</span>
                                    @endif
                                </a></li>
                                <li><a class="dropdown-item {{ request()->routeIs('admin.communications.newsletter.*') ? 'active' : '' }}"
                                       href="{{ route('admin.communications.newsletter.index') }}">
                                    <i class="fas fa-newspaper me-2"></i> Newsletter Subscribers
                                </a></li>
                            </ul>
                        </li>

                        <!-- E-commerce Management -->
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle {{ request()->routeIs('admin.categories.*', 'admin.products.*', 'admin.orders.*', 'admin.ecommerce.reports.*') ? 'active' : '' }}"
                               href="#" id="ecommerceDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-shopping-cart me-2"></i>
                                E-commerce
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item {{ request()->routeIs('admin.categories.*') ? 'active' : '' }}"
                                       href="{{ route('admin.categories.index') }}">
                                    <i class="fas fa-folder me-2"></i> Categories
                                </a></li>
                                <li><a class="dropdown-item {{ request()->routeIs('admin.products.*') ? 'active' : '' }}"
                                       href="{{ route('admin.products.index') }}">
                                    <i class="fas fa-box me-2"></i> Products
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item {{ request()->routeIs('admin.orders.*') ? 'active' : '' }}"
                                       href="{{ route('admin.orders.index') }}">
                                    <i class="fas fa-receipt me-2"></i> Orders
                                </a></li>
                                <li><a class="dropdown-item {{ request()->routeIs('admin.ecommerce.reports.*') ? 'active' : '' }}"
                                       href="{{ route('admin.ecommerce.reports.index') }}">
                                    <i class="fas fa-chart-line me-2"></i> Reports
                                </a></li>
                            </ul>
                        </li>

                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle {{ request()->routeIs('admin.users.*') ? 'active' : '' }}"
                               href="#" id="usersDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-users-cog me-2"></i>
                                User Management
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{{ route('admin.users.index') }}">
                                    <i class="fas fa-list me-2"></i> All Users
                                </a></li>
                                <li><a class="dropdown-item" href="{{ route('admin.users.create') }}">
                                    <i class="fas fa-plus me-2"></i> Add New User
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{{ route('admin.users.index', ['role' => 'admin']) }}">
                                    <i class="fas fa-user-shield me-2"></i> Admins
                                </a></li>
                                <li><a class="dropdown-item" href="{{ route('admin.users.index', ['role' => 'staff']) }}">
                                    <i class="fas fa-user-tie me-2"></i> Staff
                                </a></li>
                                <li><a class="dropdown-item" href="{{ route('admin.users.index', ['role' => 'customer']) }}">
                                    <i class="fas fa-user me-2"></i> Customers
                                </a></li>
                            </ul>
                        </li>

                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle {{ request()->routeIs('admin.customers.*', 'admin.analytics.customers') ? 'active' : '' }}"
                               href="#" id="customersDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-users me-2"></i>
                                Customers
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{{ route('admin.customers.index') }}">
                                    <i class="fas fa-list me-2"></i> Manage Customers
                                </a></li>
                                <li><a class="dropdown-item" href="{{ route('admin.analytics.customers') }}">
                                    <i class="fas fa-chart-line me-2"></i> Customer Analytics
                                </a></li>
                                <li><a class="dropdown-item" href="{{ route('admin.customers.create') }}">
                                    <i class="fas fa-plus me-2"></i> Add Customer
                                </a></li>
                            </ul>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="alert('Coming soon!')">
                                <i class="fas fa-truck me-2"></i>
                                Carriers
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('admin.cms.settings.*') ? 'active' : '' }}" href="{{ route('admin.cms.settings.index') }}">
                                <i class="fas fa-cog me-2"></i>
                                Settings
                            </a>
                        </li>
                        <hr class="my-3">
                        <li class="nav-item">
                            <a class="nav-link" href="{{ route('tracking.index') }}" target="_blank">
                                <i class="fas fa-external-link-alt me-2"></i>
                                View Website
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Sidebar Footer -->
                <div class="sidebar-footer">
                    <form method="POST" action="{{ route('logout') }}">
                        @csrf
                        <button type="submit" class="nav-link btn btn-link text-start w-100 border-0 p-0" style="color: rgba(255, 255, 255, 0.8);">
                            <i class="fas fa-sign-out-alt me-2"></i>
                            Logout
                        </button>
                    </form>
                </div>
            </div>
        </nav>

    <!-- Main content -->
    <main class="main-content" id="mainContent">
        <div class="container-fluid">
            <!-- Top Navigation -->
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom" style="margin-top: 80px;">
                <h1 class="h2">@yield('page-title', 'Dashboard')</h1>

                <!-- Global Search -->
                <div class="flex-grow-1 mx-4 d-none d-md-block" style="max-width: 500px;">
                    <div class="position-relative">
                        <input type="text"
                               id="globalSearch"
                               class="form-control form-control-lg"
                               placeholder="Search everything... (orders, users, parcels, jobs, etc.)"
                               autocomplete="off">
                        <div class="position-absolute top-50 end-0 translate-middle-y me-3">
                            <i class="fas fa-search text-muted"></i>
                        </div>

                        <!-- Search Results Dropdown -->
                        <div id="searchResults"
                             class="position-absolute w-100 bg-white border rounded-3 shadow-lg mt-1 d-none"
                             style="z-index: 1050; max-height: 400px; overflow-y: auto;">
                            <!-- Results will be populated here -->
                        </div>
                    </div>
                </div>

                <div class="d-flex align-items-center">
                    <!-- Mobile Search Button -->
                    <div class="me-3 d-md-none">
                        <button class="btn btn-outline-secondary" type="button" data-bs-toggle="modal" data-bs-target="#mobileSearchModal">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>

                    <!-- Live Chat Notification Bell -->
                    <div class="me-3">
                        <div class="dropdown">
                            <button class="btn btn-outline-primary position-relative" type="button" id="liveChatDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-bell"></i>
                                <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger" id="liveChatBadge" style="display: none;">
                                    0
                                </span>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end" style="min-width: 350px; max-height: 400px; overflow-y: auto;">
                                <li><h6 class="dropdown-header">Live Chat Notifications</h6></li>
                                <li><hr class="dropdown-divider"></li>
                                <div id="liveChatNotifications">
                                    <li class="dropdown-item text-center text-muted py-3">
                                        <i class="fas fa-comments fa-2x mb-2"></i><br>
                                        No new chat messages
                                    </li>
                                </div>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <a class="dropdown-item text-center" href="{{ route('admin.communications.live-chat.index') }}">
                                        <i class="fas fa-eye me-1"></i> View All Chats
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        @yield('page-actions')
                    </div>
                </div>
            </div>

                <!-- Alerts -->
                @if(session('success'))
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle me-2"></i>
                        {{ session('success') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                @endif

                @if(session('error'))
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        {{ session('error') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                @endif

                @if($errors->any())
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Please fix the following errors:</strong>
                        <ul class="mb-0 mt-2">
                            @foreach($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                @endif

            <!-- Page Content -->
            @yield('content')
        </div>
    </main>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Location Dropdown Library -->
    {{-- <script src="{{ asset('js/location-dropdown.js') }}"></script> --}}
    
    <!-- Currency Helper Script -->
    <script>
        // Global currency settings
        window.currencySettings = {
            symbol: '@currencySymbol',
            code: '@currencyCode',
            position: '{{ \App\Helpers\CurrencyHelper::getPosition() }}',
            decimalPlaces: {{ \App\Helpers\CurrencyHelper::getDecimalPlaces() }},
            thousandsSeparator: '{{ \App\Helpers\CurrencyHelper::getThousandsSeparator() }}',
            decimalSeparator: '{{ \App\Helpers\CurrencyHelper::getDecimalSeparator() }}'
        };

        // Currency formatting function
        function formatCurrency(amount, showSymbol = true) {
            if (amount === null || amount === undefined || amount === '') {
                amount = 0;
            }

            amount = parseFloat(amount);

            // Format the number
            const formattedAmount = amount.toLocaleString('en-US', {
                minimumFractionDigits: window.currencySettings.decimalPlaces,
                maximumFractionDigits: window.currencySettings.decimalPlaces
            });

            if (!showSymbol) {
                return formattedAmount;
            }

            // Add currency symbol
            if (window.currencySettings.position === 'before') {
                return window.currencySettings.symbol + formattedAmount;
            } else {
                return formattedAmount + ' ' + window.currencySettings.symbol;
            }
        }

        // Parse currency string to float
        function parseCurrency(currencyString) {
            if (!currencyString) return 0;

            // Remove currency symbol and spaces
            let cleaned = currencyString.toString()
                .replace(window.currencySettings.symbol, '')
                .replace(/\s/g, '');

            // Replace thousands separator
            if (window.currencySettings.thousandsSeparator !== ',') {
                cleaned = cleaned.replace(new RegExp('\\' + window.currencySettings.thousandsSeparator, 'g'), '');
            } else {
                cleaned = cleaned.replace(/,/g, '');
            }

            // Replace decimal separator with dot
            if (window.currencySettings.decimalSeparator !== '.') {
                cleaned = cleaned.replace(window.currencySettings.decimalSeparator, '.');
            }

            return parseFloat(cleaned) || 0;
        }

        // Sidebar toggle functionality
        document.addEventListener('DOMContentLoaded', function() {
            const sidebarToggle = document.getElementById('sidebarToggle');
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('mainContent');
            const sidebarOverlay = document.getElementById('sidebarOverlay');
            const toggleIcon = document.getElementById('toggleIcon');

            // Prevent horizontal scroll
            function preventHorizontalScroll() {
                document.body.style.overflowX = 'hidden';
                document.documentElement.style.overflowX = 'hidden';
            }

            // Check if sidebar should be open by default on larger screens
            function checkScreenSize() {
                preventHorizontalScroll();

                if (window.innerWidth >= 992) {
                    // Desktop: auto-open sidebar
                    sidebar.classList.add('show');
                    mainContent.classList.add('sidebar-open');
                    sidebarToggle.classList.add('sidebar-open');
                    sidebarOverlay.classList.remove('show');
                    toggleIcon.className = 'fas fa-chevron-left';
                } else {
                    // Mobile/Tablet: auto-close sidebar
                    sidebar.classList.remove('show');
                    mainContent.classList.remove('sidebar-open');
                    sidebarToggle.classList.remove('sidebar-open');
                    sidebarOverlay.classList.remove('show');
                    toggleIcon.className = 'fas fa-chevron-right';
                }
            }

            // Initial setup
            preventHorizontalScroll();
            checkScreenSize();

            // Check on window resize with debounce
            let resizeTimeout;
            window.addEventListener('resize', function() {
                clearTimeout(resizeTimeout);
                resizeTimeout = setTimeout(checkScreenSize, 100);
            });

            // Toggle sidebar
            sidebarToggle.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                const isOpen = sidebar.classList.contains('show');

                if (isOpen) {
                    // Close sidebar
                    sidebar.classList.remove('show');
                    mainContent.classList.remove('sidebar-open');
                    sidebarToggle.classList.remove('sidebar-open');
                    sidebarOverlay.classList.remove('show');
                    toggleIcon.className = 'fas fa-chevron-right';
                } else {
                    // Open sidebar
                    sidebar.classList.add('show');
                    sidebarToggle.classList.add('sidebar-open');
                    toggleIcon.className = 'fas fa-chevron-left';

                    // Only adjust main content and show overlay based on screen size
                    if (window.innerWidth >= 992) {
                        mainContent.classList.add('sidebar-open');
                        sidebarOverlay.classList.remove('show');
                    } else {
                        mainContent.classList.remove('sidebar-open');
                        sidebarOverlay.classList.add('show');
                    }
                }
            });

            // Close sidebar when clicking overlay (mobile)
            sidebarOverlay.addEventListener('click', function() {
                sidebar.classList.remove('show');
                mainContent.classList.remove('sidebar-open');
                sidebarToggle.classList.remove('sidebar-open');
                sidebarOverlay.classList.remove('show');
                toggleIcon.className = 'fas fa-chevron-right';
            });

            // Close sidebar on escape key
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && sidebar.classList.contains('show')) {
                    sidebar.classList.remove('show');
                    mainContent.classList.remove('sidebar-open');
                    sidebarToggle.classList.remove('sidebar-open');
                    sidebarOverlay.classList.remove('show');
                    toggleIcon.className = 'fas fa-chevron-right';
                }
            });

            // Prevent body scroll when sidebar is open on mobile
            function updateBodyScroll() {
                if (window.innerWidth < 992 && sidebar.classList.contains('show')) {
                    document.body.style.overflow = 'hidden';
                } else {
                    document.body.style.overflow = '';
                }
            }

            // Update body scroll on sidebar toggle
            const observer = new MutationObserver(updateBodyScroll);
            observer.observe(sidebar, { attributes: true, attributeFilter: ['class'] });

            // Live Chat Notification System
            function updateLiveChatNotifications() {
                fetch('{{ route("admin.communications.live-chat.stats") }}')
                    .then(response => response.json())
                    .then(data => {
                        const badge = document.getElementById('liveChatBadge');
                        const notificationsContainer = document.getElementById('liveChatNotifications');

                        if (data.total_unread > 0) {
                            badge.textContent = data.total_unread;
                            badge.style.display = 'block';

                            // Update notifications dropdown
                            let notificationsHtml = '';
                            data.recent_sessions.forEach(session => {
                                if (session.unread_count > 0) {
                                    notificationsHtml += `
                                        <li>
                                            <a class="dropdown-item" href="/admin/communications/live-chat/sessions/${session.id}">
                                                <div class="d-flex justify-content-between align-items-start">
                                                    <div>
                                                        <strong>${session.visitor_name}</strong>
                                                        <br>
                                                        <small class="text-muted">${session.latest_message ? session.latest_message.substring(0, 50) + '...' : 'New chat'}</small>
                                                        <br>
                                                        <small class="text-muted">${session.last_activity}</small>
                                                    </div>
                                                    <span class="badge bg-danger">${session.unread_count}</span>
                                                </div>
                                            </a>
                                        </li>
                                    `;
                                }
                            });

                            if (notificationsHtml) {
                                notificationsContainer.innerHTML = notificationsHtml;
                            }
                        } else {
                            badge.style.display = 'none';
                            notificationsContainer.innerHTML = `
                                <li class="dropdown-item text-center text-muted py-3">
                                    <i class="fas fa-comments fa-2x mb-2"></i><br>
                                    No new chat messages
                                </li>
                            `;
                        }
                    })
                    .catch(error => {
                        console.error('Error updating live chat notifications:', error);
                    });
            }

            // Update live chat notifications every 10 seconds
            updateLiveChatNotifications();
            setInterval(updateLiveChatNotifications, 10000);

            // Global Search Functionality
            const globalSearch = document.getElementById('globalSearch');
            const searchResults = document.getElementById('searchResults');
            let searchTimeout;
            let currentRequest;

            if (globalSearch && searchResults) {
                // Search input handler
                globalSearch.addEventListener('input', function() {
                    const query = this.value.trim();

                    // Clear previous timeout
                    clearTimeout(searchTimeout);

                    // Cancel previous request
                    if (currentRequest) {
                        currentRequest.abort();
                    }

                    if (query.length < 2) {
                        hideSearchResults();
                        return;
                    }

                    // Show loading state
                    showSearchLoading();

                    // Debounce search
                    searchTimeout = setTimeout(() => {
                        performSearch(query);
                    }, 300);
                });

                // Hide results when clicking outside
                document.addEventListener('click', function(e) {
                    if (!globalSearch.contains(e.target) && !searchResults.contains(e.target)) {
                        hideSearchResults();
                    }
                });

                // Show results when focusing on search input
                globalSearch.addEventListener('focus', function() {
                    if (this.value.trim().length >= 2 && searchResults.children.length > 0) {
                        showSearchResults();
                    }
                });

                // Keyboard navigation
                globalSearch.addEventListener('keydown', function(e) {
                    const items = searchResults.querySelectorAll('.search-result-item');
                    const activeItem = searchResults.querySelector('.search-result-item.active');

                    if (e.key === 'ArrowDown') {
                        e.preventDefault();
                        if (activeItem) {
                            activeItem.classList.remove('active');
                            const next = activeItem.nextElementSibling;
                            if (next && next.classList.contains('search-result-item')) {
                                next.classList.add('active');
                            } else if (items.length > 0) {
                                items[0].classList.add('active');
                            }
                        } else if (items.length > 0) {
                            items[0].classList.add('active');
                        }
                    } else if (e.key === 'ArrowUp') {
                        e.preventDefault();
                        if (activeItem) {
                            activeItem.classList.remove('active');
                            const prev = activeItem.previousElementSibling;
                            if (prev && prev.classList.contains('search-result-item')) {
                                prev.classList.add('active');
                            } else if (items.length > 0) {
                                items[items.length - 1].classList.add('active');
                            }
                        } else if (items.length > 0) {
                            items[items.length - 1].classList.add('active');
                        }
                    } else if (e.key === 'Enter') {
                        e.preventDefault();
                        if (activeItem) {
                            window.location.href = activeItem.href;
                        }
                    } else if (e.key === 'Escape') {
                        hideSearchResults();
                        this.blur();
                    }
                });
            }

            function performSearch(query) {
                const controller = new AbortController();
                currentRequest = controller;

                fetch(`{{ route('admin.search') }}?q=${encodeURIComponent(query)}&limit=20`, {
                    signal: controller.signal,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                })
                .then(response => response.json())
                .then(data => {
                    displaySearchResults(data);
                })
                .catch(error => {
                    if (error.name !== 'AbortError') {
                        console.error('Search error:', error);
                        showSearchError();
                    }
                })
                .finally(() => {
                    currentRequest = null;
                });
            }

            function displaySearchResults(data) {
                if (data.results && data.results.length > 0) {
                    let html = '';

                    data.results.forEach(result => {
                        html += `
                            <a href="${result.url}" class="search-result-item d-flex align-items-center">
                                <div class="search-result-icon ${result.type}">
                                    <i class="${result.icon}"></i>
                                </div>
                                <div class="search-result-content">
                                    <div class="search-result-title">${result.title}</div>
                                    <div class="search-result-subtitle">${result.subtitle}</div>
                                    <div class="search-result-description">${result.description}</div>
                                </div>
                                <div class="search-result-type">${result.type.replace('_', ' ')}</div>
                            </a>
                        `;
                    });

                    if (data.total > data.results.length) {
                        html += `
                            <div class="search-result-item text-center text-muted">
                                <small>Showing ${data.results.length} of ${data.total} results</small>
                            </div>
                        `;
                    }

                    searchResults.innerHTML = html;
                    showSearchResults();
                } else {
                    showNoResults(data.query);
                }
            }

            function showSearchLoading() {
                searchResults.innerHTML = `
                    <div class="search-loading">
                        <i class="fas fa-spinner fa-spin me-2"></i>
                        Searching...
                    </div>
                `;
                showSearchResults();
            }

            function showNoResults(query) {
                searchResults.innerHTML = `
                    <div class="search-no-results">
                        <i class="fas fa-search me-2"></i>
                        No results found for "${query}"
                    </div>
                `;
                showSearchResults();
            }

            function showSearchError() {
                searchResults.innerHTML = `
                    <div class="search-no-results text-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Search error occurred. Please try again.
                    </div>
                `;
                showSearchResults();
            }

            function showSearchResults() {
                searchResults.classList.remove('d-none');
            }

            function hideSearchResults() {
                searchResults.classList.add('d-none');
            }

            // Mobile Search Functionality
            const mobileGlobalSearch = document.getElementById('mobileGlobalSearch');
            const mobileSearchResults = document.getElementById('mobileSearchResults');
            let mobileSearchTimeout;
            let mobileCurrentRequest;

            if (mobileGlobalSearch && mobileSearchResults) {
                // Mobile search input handler
                mobileGlobalSearch.addEventListener('input', function() {
                    const query = this.value.trim();

                    clearTimeout(mobileSearchTimeout);

                    if (mobileCurrentRequest) {
                        mobileCurrentRequest.abort();
                    }

                    if (query.length < 2) {
                        mobileSearchResults.classList.add('d-none');
                        return;
                    }

                    // Show loading state
                    mobileSearchResults.innerHTML = `
                        <div class="text-center py-4">
                            <i class="fas fa-spinner fa-spin me-2"></i>
                            Searching...
                        </div>
                    `;
                    mobileSearchResults.classList.remove('d-none');

                    // Debounce search
                    mobileSearchTimeout = setTimeout(() => {
                        performMobileSearch(query);
                    }, 300);
                });

                // Focus on mobile search when modal opens
                document.getElementById('mobileSearchModal').addEventListener('shown.bs.modal', function() {
                    mobileGlobalSearch.focus();
                });

                // Clear mobile search when modal closes
                document.getElementById('mobileSearchModal').addEventListener('hidden.bs.modal', function() {
                    mobileGlobalSearch.value = '';
                    mobileSearchResults.classList.add('d-none');
                    mobileSearchResults.innerHTML = '';
                });
            }

            function performMobileSearch(query) {
                const controller = new AbortController();
                mobileCurrentRequest = controller;

                fetch(`{{ route('admin.search') }}?q=${encodeURIComponent(query)}&limit=20`, {
                    signal: controller.signal,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                })
                .then(response => response.json())
                .then(data => {
                    displayMobileSearchResults(data);
                })
                .catch(error => {
                    if (error.name !== 'AbortError') {
                        console.error('Mobile search error:', error);
                        mobileSearchResults.innerHTML = `
                            <div class="text-center py-4 text-danger">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                Search error occurred. Please try again.
                            </div>
                        `;
                    }
                })
                .finally(() => {
                    mobileCurrentRequest = null;
                });
            }

            function displayMobileSearchResults(data) {
                if (data.results && data.results.length > 0) {
                    let html = '<div class="list-group">';

                    data.results.forEach(result => {
                        html += `
                            <a href="${result.url}" class="list-group-item list-group-item-action" data-bs-dismiss="modal">
                                <div class="d-flex align-items-center">
                                    <div class="search-result-icon ${result.type} me-3">
                                        <i class="${result.icon}"></i>
                                    </div>
                                    <div class="flex-grow-1">
                                        <div class="fw-bold">${result.title}</div>
                                        <div class="text-muted small">${result.subtitle}</div>
                                        <div class="text-muted" style="font-size: 0.8rem;">${result.description}</div>
                                    </div>
                                    <span class="badge bg-secondary">${result.type.replace('_', ' ')}</span>
                                </div>
                            </a>
                        `;
                    });

                    html += '</div>';

                    if (data.total > data.results.length) {
                        html += `
                            <div class="text-center mt-3 text-muted">
                                <small>Showing ${data.results.length} of ${data.total} results</small>
                            </div>
                        `;
                    }

                    mobileSearchResults.innerHTML = html;
                    mobileSearchResults.classList.remove('d-none');
                } else {
                    mobileSearchResults.innerHTML = `
                        <div class="text-center py-4 text-muted">
                            <i class="fas fa-search me-2"></i>
                            No results found for "${data.query}"
                        </div>
                    `;
                    mobileSearchResults.classList.remove('d-none');
                }
            }
        });
    </script>

    <!-- Mobile Search Modal -->
    <div class="modal fade" id="mobileSearchModal" tabindex="-1" aria-labelledby="mobileSearchModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-fullscreen-sm-down">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="mobileSearchModalLabel">
                        <i class="fas fa-search me-2"></i>Global Search
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <input type="text"
                               id="mobileGlobalSearch"
                               class="form-control form-control-lg"
                               placeholder="Search everything... (orders, users, parcels, jobs, etc.)"
                               autocomplete="off">
                    </div>

                    <!-- Mobile Search Results -->
                    <div id="mobileSearchResults" class="d-none">
                        <!-- Results will be populated here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    @stack('scripts')
</body>
</html>
