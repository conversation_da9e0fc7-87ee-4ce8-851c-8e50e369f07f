@php
    $seoService = app(\App\Services\SeoLocalizationService::class);
    $currentLocale = $seoService->getCurrentLocale();
    $language = explode('-', $currentLocale)[0];
@endphp
<!DOCTYPE html>
<html lang="{{ $language }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <!-- Locale and Language Meta Tags -->
    <meta http-equiv="content-language" content="{{ $currentLocale }}">
    <meta name="language" content="{{ $language }}">

    <!-- Comprehensive SEO Meta Tags -->
    <x-seo-meta-tags
        :title="$title ?? null"
        :description="$description ?? null"
        :keywords="$keywords ?? null"
        :image="$ogImage ?? null"
        :type="$pageType ?? 'website'"
        :locale="$currentLocale"
    />

    <!-- Hreflang Tags for International SEO -->
    <x-seo-hreflang />

    <!-- Favicon -->
    @if(isset($siteSettings['site_favicon']) && $siteSettings['site_favicon'])
    <link rel="icon" type="image/x-icon" href="{{ Storage::url($siteSettings['site_favicon']) }}">
    @else
    <link rel="icon" type="image/x-icon" href="{{ asset('favicon.ico') }}">
    @endif

    <!-- Bootstrap CSS (loaded first so Tailwind can override) -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Tailwind CSS (loaded after Bootstrap to override) -->
    <script src="https://cdn.tailwindcss.com?plugins=forms,typography,aspect-ratio,line-clamp"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif'],
                        'heading': ['Poppins', 'system-ui', 'sans-serif'],
                    },
                    colors: {
                        'earth': {
                            100: '#f5f1eb',
                            200: '#e8dcc6',
                            300: '#dbc7a1',
                            400: '#ceb27c',
                            500: '#b08d57',
                            600: '#9a7a4a',
                            700: '#82653f',
                            800: '#6b5034',
                            900: '#543b29',
                        }
                    },
                    maxWidth: {
                        '8xl': '88rem',
                        '9xl': '96rem',
                    }
                },
                container: {
                    center: true,
                    padding: '1rem',
                    screens: {
                        'sm': '640px',
                        'md': '768px',
                        'lg': '1024px',
                        'xl': '1280px',
                        '2xl': '1400px',
                    }
                }
            }
        }
    </script>

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">

    <!-- Custom Styles -->
    <style>
        .hero-pattern {
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }

        .animate-on-scroll {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.8s ease;
        }

        .animate-on-scroll.animate {
            opacity: 1;
            transform: translateY(0);
        }

        .card-hover {
            transition: all 0.3s ease;
        }

        .card-hover:hover {
            transform: translateY(-8px);
        }

        .btn-primary {
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }

        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        ::-webkit-scrollbar-thumb {
            background: #16a34a;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #15803d;
        }

        /* Fix navigation underlines */
        nav a {
            text-decoration: none !important;
        }

        nav a:hover {
            text-decoration: none !important;
        }

        /* Ensure no underlines on links */
        .no-underline {
            text-decoration: none !important;
        }
    </style>

    @stack('styles')

    <!-- Structured Data -->
    <x-seo-structured-data type="organization" :data="[
        'name' => $siteSettings['site_name'] ?? 'Atrix Logistics',
        'url' => url('/'),
        'logo' => isset($siteSettings['site_logo']) ? Storage::url($siteSettings['site_logo']) : asset('images/logo.png'),
        'description' => $siteSettings['site_description'] ?? 'Professional logistics solutions',
    ]" />

    <x-seo-structured-data type="website" :data="[
        'name' => $siteSettings['site_name'] ?? 'Atrix Logistics',
        'url' => url('/'),
        'description' => $siteSettings['site_description'] ?? 'Professional logistics solutions',
    ]" />

    @stack('structured-data')
    @stack('styles')
</head>

<body class="font-sans antialiased bg-white">
    <!-- Skip to main content for accessibility -->
    <a href="#main-content" class="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-green-600 text-white px-4 py-2 rounded-lg z-50">
        Skip to main content
    </a>

    <!-- Header -->
    @include('layouts.partials.frontend.header')

    <!-- Main Content -->
    <main id="main-content">
        @yield('content')
    </main>

    <!-- Footer -->
    @include('layouts.partials.frontend.footer')

    <!-- Quote Modal -->
    @include('components.quote-modal')

    <!-- Live Chat Widget -->
    @if(isset($siteSettings['live_chat_enabled']) && $siteSettings['live_chat_enabled'])
        @include('components.live-chat-widget')
    @endif

    <!-- WhatsApp Float Button -->
    @include('components.whatsapp-float')

    <!-- Back to Top Button -->
    <button id="back-to-top" class="fixed bottom-28 right-6 bg-green-600 hover:bg-green-700 text-white p-3 rounded-full shadow-lg transition-all duration-300 opacity-0 invisible z-40">
        <i class="fas fa-arrow-up"></i>
    </button>

    <!-- Custom Styles for Mobile Menu -->
    <style>
        .hamburger.active {
            color: #16a34a !important; /* green-600 */
        }

        .hamburger.active i {
            transform: rotate(90deg);
            transition: transform 0.3s ease;
        }

        .hamburger i {
            transition: transform 0.3s ease;
        }
    </style>

    <!-- JavaScript -->
    <script>
        // Mobile menu toggle
        function toggleMobileMenu() {
            const mobileMenu = document.getElementById('mobile-menu');
            const hamburger = document.querySelector('.hamburger');

            if (!mobileMenu) {
                console.error('Mobile menu element not found');
                return;
            }

            if (mobileMenu.classList.contains('translate-x-full')) {
                // Show menu
                mobileMenu.classList.remove('translate-x-full');
                mobileMenu.classList.add('translate-x-0');
                if (hamburger) {
                    hamburger.classList.add('active');
                }
                document.body.classList.add('overflow-hidden');
            } else {
                // Hide menu
                mobileMenu.classList.remove('translate-x-0');
                mobileMenu.classList.add('translate-x-full');
                if (hamburger) {
                    hamburger.classList.remove('active');
                }
                document.body.classList.remove('overflow-hidden');
            }
        }

        // Mobile dropdown toggle
        function toggleMobileDropdown(dropdownId) {
            const dropdown = document.getElementById(dropdownId + '-dropdown');
            const icon = document.getElementById(dropdownId + '-icon');

            if (dropdown.classList.contains('hidden')) {
                dropdown.classList.remove('hidden');
                icon.classList.add('rotate-180');
            } else {
                dropdown.classList.add('hidden');
                icon.classList.remove('rotate-180');
            }
        }

        // Quote modal functions are defined in components/quote-modal.blade.php

        // Scroll animations
        function initScrollAnimations() {
            const observer = new IntersectionObserver((entries) => {
                entries.forEach((entry) => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('animate');
                    }
                });
            }, {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            });

            document.querySelectorAll('.animate-on-scroll').forEach((el) => {
                observer.observe(el);
            });
        }

        // Back to top button
        function initBackToTop() {
            const backToTopButton = document.getElementById('back-to-top');

            window.addEventListener('scroll', () => {
                if (window.pageYOffset > 300) {
                    backToTopButton.classList.remove('opacity-0', 'invisible');
                    backToTopButton.classList.add('opacity-100', 'visible');
                } else {
                    backToTopButton.classList.add('opacity-0', 'invisible');
                    backToTopButton.classList.remove('opacity-100', 'visible');
                }
            });

            backToTopButton.addEventListener('click', () => {
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            });
        }

        // Initialize everything when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            initScrollAnimations();
            initBackToTop();
        });

        // Close mobile menu when clicking outside
        document.addEventListener('click', function(event) {
            const mobileMenu = document.getElementById('mobile-menu');
            const hamburger = document.querySelector('.hamburger');
            const mobileMenuButton = document.querySelector('[onclick="toggleMobileMenu()"]');

            if (mobileMenu && mobileMenuButton && !mobileMenu.contains(event.target) && !mobileMenuButton.contains(event.target) && !mobileMenu.classList.contains('translate-x-full')) {
                // Hide menu
                mobileMenu.classList.remove('translate-x-0');
                mobileMenu.classList.add('translate-x-full');
                if (hamburger) {
                    hamburger.classList.remove('active');
                }
                document.body.classList.remove('overflow-hidden');
            }
        });
    </script>

    <!-- Currency Helper Script -->
    <script>
        // Global currency settings
        window.currencySettings = {
            symbol: '@currencySymbol',
            code: '@currencyCode',
            position: '{{ \App\Helpers\CurrencyHelper::getPosition() }}',
            decimalPlaces: {{ \App\Helpers\CurrencyHelper::getDecimalPlaces() }},
            thousandsSeparator: '{{ \App\Helpers\CurrencyHelper::getThousandsSeparator() }}',
            decimalSeparator: '{{ \App\Helpers\CurrencyHelper::getDecimalSeparator() }}'
        };

        // Currency formatting function
        function formatCurrency(amount, showSymbol = true) {
            if (amount === null || amount === undefined || amount === '') {
                amount = 0;
            }

            amount = parseFloat(amount);

            // Format the number
            const formattedAmount = amount.toLocaleString('en-US', {
                minimumFractionDigits: window.currencySettings.decimalPlaces,
                maximumFractionDigits: window.currencySettings.decimalPlaces
            });

            if (!showSymbol) {
                return formattedAmount;
            }

            // Add currency symbol
            if (window.currencySettings.position === 'before') {
                return window.currencySettings.symbol + formattedAmount;
            } else {
                return formattedAmount + ' ' + window.currencySettings.symbol;
            }
        }

        // Parse currency string to float
        function parseCurrency(currencyString) {
            if (!currencyString) return 0;

            // Remove currency symbol and spaces
            let cleaned = currencyString.toString()
                .replace(window.currencySettings.symbol, '')
                .replace(/\s/g, '');

            // Replace thousands separator
            if (window.currencySettings.thousandsSeparator !== ',') {
                cleaned = cleaned.replace(new RegExp('\\' + window.currencySettings.thousandsSeparator, 'g'), '');
            } else {
                cleaned = cleaned.replace(/,/g, '');
            }

            // Replace decimal separator with dot
            if (window.currencySettings.decimalSeparator !== '.') {
                cleaned = cleaned.replace(window.currencySettings.decimalSeparator, '.');
            }

            return parseFloat(cleaned) || 0;
        }
    </script>

    @stack('scripts')

    <!-- Notification System for Mockups -->
    <div id="notification-container" class="fixed top-4 right-4 z-50 space-y-2"></div>

    <script>
    // Global notification function for mockups
    function showNotification(message, type = 'info', duration = 3000) {
        const container = document.getElementById('notification-container');
        const notification = document.createElement('div');

        const bgColor = type === 'success' ? 'bg-green-500' :
                       type === 'error' ? 'bg-red-500' :
                       type === 'warning' ? 'bg-yellow-500' : 'bg-blue-500';

        notification.className = `${bgColor} text-white px-4 py-3 rounded-lg shadow-lg transform translate-x-full transition-transform duration-300 max-w-sm`;
        notification.innerHTML = `
            <div class="flex items-center justify-between">
                <span class="text-sm font-medium">${message}</span>
                <button onclick="this.parentElement.parentElement.remove()" class="ml-3 text-white hover:text-gray-200">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        container.appendChild(notification);

        // Animate in
        setTimeout(() => {
            notification.classList.remove('translate-x-full');
        }, 100);

        // Auto remove
        setTimeout(() => {
            notification.classList.add('translate-x-full');
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 300);
        }, duration);
    }

    // Update cart count on page load
    document.addEventListener('DOMContentLoaded', function() {
        @auth
        updateCartCount();
        @endauth
    });

    // Update cart count function
    function updateCartCount() {
        fetch('/cart/count')
            .then(response => response.json())
            .then(data => {
                const cartCountElement = document.querySelector('.cart-count');
                if (cartCountElement) {
                    cartCountElement.textContent = data.count || 0;

                    // Hide/show cart count badge
                    if (data.count > 0) {
                        cartCountElement.classList.remove('hidden');
                    } else {
                        cartCountElement.classList.add('hidden');
                    }
                }
            })
            .catch(error => console.error('Error updating cart count:', error));
    }
    </script>

    <!-- Bootstrap JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>



    @stack('scripts')
</body>
</html>
